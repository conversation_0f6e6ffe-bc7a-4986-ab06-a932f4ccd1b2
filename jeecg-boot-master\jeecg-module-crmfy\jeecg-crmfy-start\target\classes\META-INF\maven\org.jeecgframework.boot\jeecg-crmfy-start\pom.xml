<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-module-crmfy</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.6.3</version>
        <!--<relativePath>../../pom.xml</relativePath>-->
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jeecg-crmfy-start</artifactId>

    <properties>
        <tomcat.version>9.0.100</tomcat.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-crmfy-biz</artifactId>
            <version>${jeecgboot.version}</version>
        </dependency>
        <!--人大金仓
        <dependency>
            <groupId>org.jeecgframework</groupId>
            <artifactId>kingbase8</artifactId>
            <version>9.0.0</version>
            <scope>runtime</scope>
        </dependency>-->
        <!--达梦数据库 -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>Dm8JdbcDriver18</artifactId>
            <version>${dm8.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmDialect-for-hibernate5.0</artifactId>
            <version>${dm8.version}</version>
        </dependency>

        <!-- flyway支持 mysql5.7、mysql8、MariaDB10.3.16 -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>