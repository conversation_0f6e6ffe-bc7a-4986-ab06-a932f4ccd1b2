import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

// 列表页表格列定义
export const columns: BasicColumn[] = [
  {
    title: '保单号',
    dataIndex: 'policyNo',
    width: 150,
  },
  {
    title: '客户',
    dataIndex: 'customerName',
    width: 120,
  },
  {
    title: '保险产品名称',
    dataIndex: 'productName',
    width: 180,
  },
  {
    title: '保险产品名称(英文)',
    dataIndex: 'planName',
    width: 180,
  },
  {
    title: '供应商',
    dataIndex: 'supplierNo_dictText',
    width: 120,
  },
  {
    title: '保单状态',
    dataIndex: 'policyStatus_dictText',
    width: 120,
  },
  {
    title: '缴费金额',
    dataIndex: 'paymentAmount',
    width: 120,
  },
  {
    title: '缴费频率',
    dataIndex: 'paymentFrequency_dictText',
    width: 100,
  },
  {
    title: '缴费年期',
    dataIndex: 'paymentTerm',
    width: 100,
  },
  {
    title: '通知电邮地址',
    dataIndex: 'notificationEmail',
    width: 180,
  },
  {
    title: '缴费方式',
    dataIndex: 'paymentMethod_dictText',
    width: 100,
  },
  {
    title: '缴费币种',
    dataIndex: 'paymentCurrency_dictText',
    width: 100,
  },
  {
    title: '销售员',
    dataIndex: 'salesUsername_dictText',
    width: 120,
  },
];

// 搜索表单定义
export const searchFormSchema: FormSchema[] = [
  {
    field: 'customerId',
    label: '客户',
    component: 'JSelectCustomer',
    componentProps: {
      placeholder: '请输入客户姓名或手机号搜索',
    },
    colProps: { span: 6 },
  },
  {
    field: 'policyNo',
    label: '保单号码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'planName',
    label: '产品名称(英文)',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'policyStatus',
    label: '保单状态',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'policy_status',
      placeholder: '请选择保单状态',
    },
    colProps: { span: 6 },
  },
  {
    field: 'productName',
    label: '保险产品名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'supplierNo',
    label: '供应商',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
      placeholder: '请选择供应商',
    },
    colProps: { span: 6 },
  },
  {
    field: 'izActive',
    label: '是否最新版本',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
      placeholder: '是否最新版本',
    },
    defaultValue: '1',
    colProps: { span: 6 },
  },
];

// 基础表单字段（投保人、被保人、受益人共用）
const personFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '姓名',
    component: 'Input',
    required: true,
  },
  {
    field: 'nationality',
    label: '国籍',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'nationality',
    },
    required: true,
  },
  {
    field: 'idType',
    label: '证件类型',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'id_type',
    },
    required: true,
  },
  {
    field: 'idNumber',
    label: '证件号码',
    component: 'Input',
    required: true,
  },
  {
    field: 'idAddress',
    label: '证件地址',
    component: 'InputTextArea',
    required: true,
  },
  {
    field: 'contactAddress',
    label: '通讯地址',
    component: 'InputTextArea',
    required: true,
  },
  {
    field: 'mobile',
    label: '手机号码',
    component: 'Input',
    required: true,
  },
  {
    field: 'occupation',
    label: '工作职位',
    component: 'Input',
    required: true,
  },
  {
    field: 'companyAddress',
    label: '单位地址',
    component: 'InputTextArea',
    required: true,
  },
  {
    field: 'email',
    label: '电子邮箱',
    component: 'Input',
    required: true,
  },
  {
    field: 'bankAccountName',
    label: '银行开户名',
    component: 'Input',
    required: true,
  },
  {
    field: 'bankName',
    label: '银行名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'bankCurrency',
    label: '账户币种',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'payment_currency',
    },
    required: true,
  },
  {
    field: 'bankAccount',
    label: '银行账号',
    component: 'Input',
    required: true,
  },
];

// 保单表单定义
export const policyFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'customerId',
    label: '客户',
    component: 'JSelectCustomer',
    componentProps: {
      placeholder: '请输入客户姓名或手机号搜索',
    },
    required: true,
  },
  {
    field: 'supplierNo',
    label: '供应商',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
    },
    required: true,
  },
  {
    field: 'productId',
    label: '保险产品',
    component: 'JSelectProduct',
    slot: 'productId',
    required: true,
  },
  {
    field: 'productName',
    label: '产品名称',
    component: 'Input',
    dynamicDisabled: false,
    show: false,

  },
  {
    field: 'planName',
    label: '产品名称(英文)',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    field: 'policyNo',
    label: '保单号码',
    component: 'Input',
    required: ({ values }) => {
      // 当核保结果为不通过(0)时必填
      return values.policyStatus === '7';
    },
  },
  {
    field: 'paymentAmount',
    label: '缴费金额',
    component: 'InputNumber',
    required: true,
  },
  {
    field: 'paymentFrequency',
    label: '缴费频率',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'payment_frequency',
    },
    required: true,
  },
  {
    field: 'paymentTerm',
    label: '缴费年期',
    component: 'InputNumber',
    required: true,
  },
  {
    field: 'paymentCurrency',
    label: '缴费币种',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'payment_currency',
    },
    required: true,
  },
  {
    field: 'paymentMethod',
    label: '缴费方式',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'payment_method',
    },
    required: true,
  },
  {
    field: 'coveragePeriod',
    label: '保障期限',
    component: 'Input',
    required: true,
  },
  {
    field: 'autoDebit',
    label: '是否自动扣款',
    component: 'Switch',
    defaultValue: false,
  },
  {
    field: 'promotionPlan',
    label: '优惠方案',
    component: 'Input',
  },
  {
    field: 'notificationEmail',
    label: '通知电邮地址',
    component: 'Input',
    required: true,
  },
  {
    field: 'policyStatus',
    label: '保单状态',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'policy_status',
    },
    dynamicDisabled: true,
  },

  {
    field: 'auditResult',
    label: '核保是否通过',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
    },
    // 默认隐藏，由Modal中的updateFormFieldsConfig动态控制
    ifShow: false,
    required: false,
  },

  {
    field: 'noticeRemark',
    label: '照会说明',
    component: 'InputTextArea',
    dynamicDisabled:  ({ values }) => {
      // 当销售员照会回复时，照会说明不可编辑
      return values.policyStatus=== '3';
    },
    // 仅在核保不通过时显示且必填
    ifShow: ({ values }) => {
      // 当核保结果为不通过(0)时显示
      return values.auditResult === '0'|| values.policyStatus=== '3';
    },
    required: ({ values }) => {
      // 当核保结果为不通过(0)时必填
      return values.auditResult === '0';
    },
  },
  {
    field: 'noticeReply',
    label: '照会回复',
    component: 'InputTextArea',
    dynamicDisabled: false,
    // 默认隐藏，由Modal中的updateFormFieldsConfig动态控制
    ifShow: false,
    required: false,
  },
  {
    field: 'changeReason',
    label: '变更原因',
    component: 'InputTextArea',
    dynamicDisabled: false,
    // 默认隐藏，由Modal中的updateFormFieldsConfig动态控制
    ifShow: false,
    required: false,
  },

  {
    field: 'additionalInsurance',
    label: '附加险',
    component: 'Input',
    slot: 'additionalInsurance',
    colProps: { span: 24 }, // 设置为一行一列
  },
  {
    field: 'policyHolderInfo',
    label: '投保人信息',
    component: 'Input',
    slot: 'policyHolderInfo',
    colProps: { span: 24 }, // 设置为一行一列
  },
  {
    field: 'insuredInfo',
    label: '被保人信息',
    component: 'Input',
    slot: 'insuredInfo',
    colProps: { span: 24 }, // 设置为一行一列
  },
  {
    field: 'beneficiaryInfo',
    label: '受益人信息',
    component: 'Input',
    slot: 'beneficiaryInfo',
    colProps: { span: 24 }, // 设置为一行一列
  },
];

// 导出投保人表单
export const policyHolderFormSchema = personFormSchema;

// 导出被保人表单
export const insuredFormSchema = personFormSchema;

// 导出受益人表单
export const beneficiaryFormSchema = personFormSchema;

// 附加险表单
export const additionalInsuranceFormSchema: FormSchema[] = [
  {
    field: 'supplierNo',
    label: '供应商',
    component: 'Input',
    show: false, // 隐藏字段，仅用于传递供应商编码
  },
  {
    field: 'productId',
    label: '附加险产品',
    component: 'JSelectProduct',
    slot: 'productId', // 使用插槽渲染，确保能正确接收supplierNo
    required: true,
  },
  {
    field: 'productName',
    label: '产品名称',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    field: 'planName',
    label: '产品名称(英文)',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    field: 'additionalAmount',
    label: '每期保费',
    component: 'InputNumber',
    required: true,
  },
];
