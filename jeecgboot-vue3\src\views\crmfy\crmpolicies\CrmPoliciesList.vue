<template>
  <div>
    <!-- 引用表格 -->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!-- 插槽:table标题 -->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" v-auth="'policyorder:add'"  preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
      </template>

      <!-- 供应商列自定义渲染 -->
      <template #supplierNo_dictText="{ text }">
        <a-tag color="blue">{{ text }}</a-tag>
      </template>

      <!-- 保单状态列自定义渲染 -->
      <template #policyStatus_dictText="{ text }">
        <a-tag color="blue">{{ text }}</a-tag>
      </template>

      <!-- 缴费频率列自定义渲染 -->
      <template #paymentFrequency_dictText="{ text }">
        <a-tag color="blue">{{ text }}</a-tag>
      </template>

      <!-- 缴费方式列自定义渲染 -->
      <template #paymentMethod_dictText="{ text }">
        <a-tag color="blue">{{ text }}</a-tag>
      </template>

      <!-- 缴费币种列自定义渲染 -->
      <template #paymentCurrency_dictText="{ text }">
        <a-tag color="blue">{{ text }}</a-tag>
      </template>

      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单弹窗 -->
    <CrmPoliciesModal @register="registerModal" @success="handleSuccess" />

    <!-- 详情弹窗 -->
    <CrmPoliciesDetail @register="registerDetailModal" />
  </div>
</template>

<script lang="ts" name="crmfy-crmPolicies" setup>
  import { computed } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import CrmPoliciesModal from './modules/CrmPoliciesModal.vue';
  import CrmPoliciesDetail from './modules/CrmPoliciesDetail.vue';
  import { columns, searchFormSchema } from './CrmPolicies.data';
  import { list, deleteOne, batchDelete, getExportUrl } from '/@/api/crmfy/crmPolicies.api';
  import { Icon } from '/@/components/Icon';
  import { useUserStore } from '/@/store/modules/user';

  // 注册modal
  const [registerModal, { openModal }] = useModal();
  const [registerDetailModal, { openModal: openDetailModal }] = useModal();

  // 获取当前用户信息
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;

  // 判断用户角色 - 使用可选链和类型断言来避免TypeScript错误
  const isSalesRole = computed(() => (userInfo as any)?.postCode === 'customerManager'); // 销售客户经理
  const isOperationRole = computed(() => (userInfo as any)?.postCode === 'operationalPost'); // 运营人员

  // 开发调试用，查看用户信息结构
  console.log('用户信息:', userInfo);

  // 注册table数据
  const { tableContext, onExportXls } = useListPage({
    tableProps: {
      title: '订单信息管理',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
      },
      actionColumn: {
        width: 180,
      },
    },
    exportConfig: {
      name: "订单信息管理",
      url: getExportUrl,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openDetailModal(true, {
      record,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteOne({ id: record.id }, reload);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    batchDelete({ ids: selectedRowKeys.value }, reload);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * 运营审核
   */
  function handleOperationAudit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
      actionType: 'operationAudit',
    });
  }

  /**
   * 核保回复
   */
  function handleUnderwritingReply(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
      actionType: 'underwritingReply',
    });
  }

  /**
   * 照会回复
   */
  function handleNoticeReply(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
      actionType: 'noticeReply',
    });
  }

  /**
   * 保费到账
   */
  function handleFeesArrival(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
      actionType: 'feesArrival',
    });
  }

  /**
   * 保单签发
   */
  function handlePolicyIssuance(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
      actionType: 'policyIssuance',
    });
  }

  /**
   * 订单完成
   */
  function handlePolicyComplete(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
      actionType: 'policyComplete',
    });
  }

  /**
   * 订单完成后编辑
   */
  function handlePolicyEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
      actionType: 'policyEdit',
    });
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable) {
    const actions = [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }
    ];

    // 根据保单状态和用户角色添加不同的操作按钮
    if (isSalesRole.value) {
      // 销售员操作
      if (record.policyStatus === '0') {
        // 草稿状态
        actions.push({
          label: '编辑',
          onClick: handleEdit.bind(null, record),
        });
      } else if (record.policyStatus === '3') {
        // 照会待回复
        actions.push({
          label: '照会回复',
          onClick: handleNoticeReply.bind(null, record),
        });
      }
    } else if (isOperationRole.value) {
      // 运营岗操作
      if (record.policyStatus === '1') {
        // 已签单待提交
        actions.push({
          label: '审核提交',
          onClick: handleOperationAudit.bind(null, record),
        });
      } else if (record.policyStatus === '2' || record.policyStatus === '4') {
        // 已提交待核保 或 照会已回复待核保
        actions.push({
          label: '核保回复',
          onClick: handleUnderwritingReply.bind(null, record),
        });
      } else if (record.policyStatus === '5') {
        // 核保通过待保费到账
        actions.push({
          label: '保费到账',
          onClick: handleFeesArrival.bind(null, record),
        });
      } else if (record.policyStatus === '6') {
        // 保费到账待签发保单
        actions.push({
          label: '保单签发',
          onClick: handlePolicyIssuance.bind(null, record),
        });
      } else if (record.policyStatus === '7') {
        // 保单签发
        actions.push({
          label: '订单完成',
          onClick: handlePolicyComplete.bind(null, record),
        });
      }
    }

    return actions;
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record: Recordable) {
    const actions: any[] = [];

    // 只有草稿状态的保单可以删除，且只有销售员可以删除
    if (isSalesRole.value && record.policyStatus === '0') {
      actions.push({
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'left',
          overlayClassName: 'table-delete-confirm'
        }
      });
    }

    return actions;
  }
</script>

<style scoped></style>





