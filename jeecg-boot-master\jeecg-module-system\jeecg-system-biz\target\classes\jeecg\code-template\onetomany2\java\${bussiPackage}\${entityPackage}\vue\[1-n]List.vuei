<#list subTables as subTab>
#segment#${subTab.entityName}List.vue
<template>
  <a-card :bordered="false">

    <!-- 操作按钮区域 -->
    <div class="table-operator" :md="24" :sm="24" style="margin: 0px 0px 20px 0px">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>

      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical"/>
          <a-dropdown>
            <a class="ant-dropdown-link">
              更多 <a-icon type="down"/>
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;" @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <${subTab.entityName}-modal ref="modalForm" @ok="modalFormOk"/>
  </a-card>
</template>

<script>
  import ${subTab.entityName}Modal from './modules/${subTab.entityName}Modal'
  import {JeecgListMixin} from '@/mixins/JeecgListMixin'
  import {getAction} from '@/api/manage'

  export default {
    name: "${subTab.entityName}List",
    mixins: [JeecgListMixin],
    components: {
      ${subTab.entityName}Modal,
    },
    data() {
      return {
        description: '信息',
        // 表头
        columns: [
        <#list subTab.colums as po><#rt/>
  <#if po.fieldName !='id' && subTab.foreignKeys[0]?uncap_first != po.fieldName>
          {
            title: '${po.filedComment}',
            align:"center",
            dataIndex: '${po.fieldName}'
          },
  </#if>
</#list>
         {
          title: '操作',
          key: 'operation',
          align: "center",
          width: 130,
          scopedSlots: {customRender: 'action'},
        }],
        url: {
          list: "/${entityPackage}/${entityName?uncap_first}/list${subTab.entityName}ByMainId",
          delete: "/${entityPackage}/${entityName?uncap_first}/delete${subTab.entityName}",
          deleteBatch: "/${entityPackage}/${entityName?uncap_first}/deleteBatch${subTab.entityName}",
        }
      }
    },
    methods: {
      loadData(arg) {
        if (arg === 1) {
          this.ipagination.current = 1;
        }
        var params = this.getQueryParams();
        <#-- update-begin--Author:kangxiaolin  Date:20190905 for：[442]主子表分开维护，生成的代码子表的分页改为真实的分页-------------------- -->
        getAction(this.url.list, {  <#list subTab.foreignKeys as key>  <#if key?lower_case?index_of("${primaryKeyField}")!=-1>${key?uncap_first} </#if>                  </#list>   : params.mainId
        , pageNo : this.ipagination.current, pageSize :this.ipagination.pageSize}).then((res) => {
          if (res.success) {
           this.dataSource = res.result.records;
           this.ipagination.total = res.result.total;
          } else {
            this.dataSource = null;
          }
        })
         <#-- update-end--Author:kangxiaolin  Date:20190905 for：[442]主子表分开维护，生成的代码子表的分页改为真实的分页-------------------- -->
      },
      getMain(mainId) {
        this.queryParam.mainId = mainId;
        this.loadData(1);
      },
      handleAdd: function () {
        this.$refs.modalForm.add(this.queryParam.mainId);
        this.$refs.modalForm.title = "添加${subTab.ftlDescription}";
      },
    }
  }
</script>
<style scoped>
  .ant-card {
    margin-left: -30px;
    margin-right: -30px;
  }
</style>
</#list>