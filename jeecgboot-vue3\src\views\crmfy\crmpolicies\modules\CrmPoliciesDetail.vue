<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="80%">
    <a-spin :spinning="loading">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="基本信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="保单号">{{ policyDetail.policyNo }}</a-descriptions-item>
            <a-descriptions-item label="客户">{{ policyDetail.customerId_dictText}}</a-descriptions-item>
            <a-descriptions-item label="保险产品名称">{{ policyDetail.productName }}</a-descriptions-item>
            <a-descriptions-item label="保险产品名称(英文)">{{ policyDetail.planName }}</a-descriptions-item>
            <a-descriptions-item label="供应商">
              <a-tag color="blue">{{ getDictText('supplier', policyDetail.supplierNo) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="保单状态">
              <a-tag color="blue">{{ getDictText('policy_status', policyDetail.policyStatus) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="缴费金额">{{ policyDetail.paymentAmount }}</a-descriptions-item>
            <a-descriptions-item label="缴费频率">
              <a-tag color="blue">{{ getDictText('payment_frequency', policyDetail.paymentFrequency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="缴费年期">{{ policyDetail.paymentTerm }}</a-descriptions-item>
            <a-descriptions-item label="通知电邮地址">{{ policyDetail.notificationEmail }}</a-descriptions-item>
            <a-descriptions-item label="缴费方式">
              <a-tag color="blue">{{ getDictText('payment_method', policyDetail.paymentMethod) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="缴费币种">
              <a-tag color="blue">{{ getDictText('payment_currency', policyDetail.paymentCurrency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="订单状态">
              <a-tag color="blue">{{ getDictText('policy_status', policyDetail.policyStatus) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="销售员">{{ policyDetail.salesUsername_dictText }}</a-descriptions-item>
            <a-descriptions-item label="优惠方案">{{ policyDetail.promotionPlan }}</a-descriptions-item>
            <a-descriptions-item label="是否自动扣款">
              <a-tag :color="policyDetail.autoDebit ? 'green' : 'red'">
                {{ policyDetail.autoDebit ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="保障期限">{{ policyDetail.coveragePeriod }}</a-descriptions-item>
          </a-descriptions>

          <!-- 附加险列表 -->
          <a-divider orientation="left">附加险列表</a-divider>
          <a-table
            :dataSource="policyDetail.crmPolicyAdditionals || []"
            :columns="additionalColumns"
            :pagination="false"
            bordered
            size="small"
          />
        </a-tab-pane>

        <a-tab-pane key="2" tab="投保人信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="姓名">{{ policyDetail.crmPolicyHolders?.name }}</a-descriptions-item>
            <a-descriptions-item label="国籍">
              <a-tag color="blue">{{ getDictText('nationality', policyDetail.crmPolicyHolders?.nationality) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件类型">
              <a-tag color="blue">{{ getDictText('id_type', policyDetail.crmPolicyHolders?.idType) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件号码">{{ policyDetail.crmPolicyHolders?.idNumber }}</a-descriptions-item>
            <a-descriptions-item label="证件地址" :span="2">{{ policyDetail.crmPolicyHolders?.idAddress }}</a-descriptions-item>
            <a-descriptions-item label="通讯地址" :span="2">{{ policyDetail.crmPolicyHolders?.contactAddress }}</a-descriptions-item>
            <a-descriptions-item label="手机号码">{{ policyDetail.crmPolicyHolders?.mobile }}</a-descriptions-item>
            <a-descriptions-item label="工作职位">{{ policyDetail.crmPolicyHolders?.occupation }}</a-descriptions-item>
            <a-descriptions-item label="单位地址" :span="2">{{ policyDetail.crmPolicyHolders?.companyAddress }}</a-descriptions-item>
            <a-descriptions-item label="电子邮箱">{{ policyDetail.crmPolicyHolders?.email }}</a-descriptions-item>
            <a-descriptions-item label="银行开户名">{{ policyDetail.crmPolicyHolders?.bankAccountName }}</a-descriptions-item>
            <a-descriptions-item label="银行名称">{{ policyDetail.crmPolicyHolders?.bankName }}</a-descriptions-item>
            <a-descriptions-item label="账户币种">
              <a-tag color="blue">{{ getDictText('payment_currency', policyDetail.crmPolicyHolders?.bankCurrency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="银行账号">{{ policyDetail.crmPolicyHolders?.bankAccount }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>

        <a-tab-pane key="3" tab="被保人信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="姓名">{{ policyDetail.crmPolicyInsureds?.name }}</a-descriptions-item>
            <a-descriptions-item label="国籍">
              <a-tag color="blue">{{ getDictText('nationality', policyDetail.crmPolicyInsureds?.nationality) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件类型">
              <a-tag color="blue">{{ getDictText('id_type', policyDetail.crmPolicyInsureds?.idType) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件号码">{{ policyDetail.crmPolicyInsureds?.idNumber }}</a-descriptions-item>
            <a-descriptions-item label="证件地址" :span="2">{{ policyDetail.crmPolicyInsureds?.idAddress }}</a-descriptions-item>
            <a-descriptions-item label="通讯地址" :span="2">{{ policyDetail.crmPolicyInsureds?.contactAddress }}</a-descriptions-item>
            <a-descriptions-item label="手机号码">{{ policyDetail.crmPolicyInsureds?.mobile }}</a-descriptions-item>
            <a-descriptions-item label="工作职位">{{ policyDetail.crmPolicyInsureds?.occupation }}</a-descriptions-item>
            <a-descriptions-item label="单位地址" :span="2">{{ policyDetail.crmPolicyInsureds?.companyAddress }}</a-descriptions-item>
            <a-descriptions-item label="电子邮箱">{{ policyDetail.crmPolicyInsureds?.email }}</a-descriptions-item>
            <a-descriptions-item label="银行开户名">{{ policyDetail.crmPolicyInsureds?.bankAccountName }}</a-descriptions-item>
            <a-descriptions-item label="银行名称">{{ policyDetail.crmPolicyInsureds?.bankName }}</a-descriptions-item>
            <a-descriptions-item label="账户币种">
              <a-tag color="blue">{{ getDictText('payment_currency', policyDetail.crmPolicyInsureds?.bankCurrency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="银行账号">{{ policyDetail.crmPolicyInsureds?.bankAccount }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>

        <a-tab-pane key="4" tab="受益人信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="姓名">{{ policyDetail.crmPolicyBeneficiaries?.name }}</a-descriptions-item>
            <a-descriptions-item label="国籍">
              <a-tag color="blue">{{ getDictText('nationality', policyDetail.crmPolicyBeneficiaries?.nationality) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件类型">
              <a-tag color="blue">{{ getDictText('id_type', policyDetail.crmPolicyBeneficiaries?.idType) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="证件号码">{{ policyDetail.crmPolicyBeneficiaries?.idNumber }}</a-descriptions-item>
            <a-descriptions-item label="证件地址" :span="2">{{ policyDetail.crmPolicyBeneficiaries?.idAddress }}</a-descriptions-item>
            <a-descriptions-item label="通讯地址" :span="2">{{ policyDetail.crmPolicyBeneficiaries?.contactAddress }}</a-descriptions-item>
            <a-descriptions-item label="手机号码">{{ policyDetail.crmPolicyBeneficiaries?.mobile }}</a-descriptions-item>
            <a-descriptions-item label="工作职位">{{ policyDetail.crmPolicyBeneficiaries?.occupation }}</a-descriptions-item>
            <a-descriptions-item label="单位地址" :span="2">{{ policyDetail.crmPolicyBeneficiaries?.companyAddress }}</a-descriptions-item>
            <a-descriptions-item label="电子邮箱">{{ policyDetail.crmPolicyBeneficiaries?.email }}</a-descriptions-item>
            <a-descriptions-item label="银行开户名">{{ policyDetail.crmPolicyBeneficiaries?.bankAccountName }}</a-descriptions-item>
            <a-descriptions-item label="银行名称">{{ policyDetail.crmPolicyBeneficiaries?.bankName }}</a-descriptions-item>
            <a-descriptions-item label="账户币种">
              <a-tag color="blue">{{ getDictText('payment_currency', policyDetail.crmPolicyBeneficiaries?.bankCurrency) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="银行账号">{{ policyDetail.crmPolicyBeneficiaries?.bankAccount }}</a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, h } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getPolicyDetail } from '/@/api/crmfy/crmPolicies.api';
  import { CrmPolicyDTO } from '/@/types/crmfy/crmPolicies';
  import { message, Tag } from 'ant-design-vue';
  import { getDictItemsByCode } from '/@/utils/dict/index';

  // 定义附加险表格列
  const additionalColumns = [
    {
      title: '保险产品名称(英文)',
      dataIndex: 'planName',
      key: 'planName',
    },
    {
      title: '每期保费金额',
      dataIndex: 'additionalAmount',
      key: 'additionalAmount',
    },
  ];

  // 定义变量
  const loading = ref<boolean>(false);
  const activeKey = ref<string>('1');
  const policyDetail = ref<CrmPolicyDTO>({});

  // 设置标题
  const title = computed(() => '保单详情');

  // 字典翻译函数
  const getDictText = (dictCode: string, value: string | number | undefined) => {
    if (!value || !dictCode) return value || '';
    const dictItems = getDictItemsByCode(dictCode);
    if (dictItems && dictItems.length > 0) {
      const item = dictItems.find((item: any) => item.value == value);
      return item ? item.text : value;
    }
    return value;
  };

  // 注册弹窗
  const [registerModal] = useModalInner(async (data) => {
    // 重置数据
    policyDetail.value = {};
    activeKey.value = '1';

    // 加载详情数据
    if (data.record) {
      loading.value = true;
      try {
        const result = await getPolicyDetail(data.record.id);
        policyDetail.value = result || {};
      } catch (error) {
        console.error('获取保单详情失败', error);
        message.error('获取保单详情失败');
      } finally {
        loading.value = false;
      }
    }
  });
</script>

<style lang="less" scoped>
  :deep(.ant-descriptions-item-label) {
    width: 150px;
    font-weight: bold;
  }
</style>
