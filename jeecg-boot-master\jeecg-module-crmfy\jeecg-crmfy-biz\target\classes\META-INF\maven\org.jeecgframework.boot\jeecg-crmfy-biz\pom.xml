<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-module-crmfy</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.6.3</version>
        <!--<relativePath>../../pom.xml</relativePath>-->
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jeecg-crmfy-biz</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-system-biz</artifactId>
            <version>${jeecgboot.version}</version>
        </dependency>


       <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.1</version>
        </dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>compile</scope>
        </dependency>
        
        
        <!-- PDFBox核心库 -->
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.27</version>
		</dependency>

    </dependencies>
</project>

