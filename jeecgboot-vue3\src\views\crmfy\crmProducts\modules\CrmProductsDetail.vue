<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="产品详情" width="80%" :canFullscreen="true">
    <Spin :spinning="loading">
      <a-descriptions bordered :column="2" size="middle">
        <a-descriptions-item label="公司名称">
          {{ productDetail.companyCode_dictText }}
        </a-descriptions-item>
        <a-descriptions-item label="产品名称">{{ productDetail.productName }}</a-descriptions-item>
        <a-descriptions-item label="产品名称英文">{{ productDetail.planName }}</a-descriptions-item>
        <a-descriptions-item label="产品类型">
          {{ productDetail.categoryCode_dictText }}
        </a-descriptions-item>
        <a-descriptions-item label="是否热门产品">
          <a-tag :color="productDetail.hot ? 'red' : 'default'">
            {{ productDetail.hot ? '是' : '否' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="产品唯一编码">{{ productDetail.productCode }}</a-descriptions-item>

        <a-descriptions-item label="支持币种">
          <a-tag v-for="currency in productDetail.currencies" :key="currency" color="blue">
            {{ getCurrencyLabel(currency) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="缴费频率">
          <a-tag v-for="frequency in productDetail.frequencies" :key="frequency" color="green">
            {{ getFrequencyLabel(frequency) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="产品描述" :span="2">
          <div v-html="productDetail.desc"></div>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 缴费年期及对应合约手续费率 -->
      <div class="detail-section">
        <h3>缴费年期及对应合约手续费率</h3>
        <a-table
          :dataSource="productDetail.paymentTerms || []"
          :columns="paymentTermColumns"
          :pagination="false"
          rowKey="id"
          bordered
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'contractRates'">
              <div v-for="(rate, index) in record.contractRates" :key="index" class="contract-rate-item">
                <span class="year-label">第{{ rate.yearSeq }}年:</span>
                <span class="rate-value">{{ rate.contractRate }}%</span>
              </div>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 产品文件 -->
      <div class="detail-section">
        <h3>产品文件</h3>
        <a-table
          :dataSource="productDetail.files || []"
          :columns="fileColumns"
          :pagination="false"
          rowKey="id"
          bordered
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button type="link" @click="downloadFile(record)">
                <Icon icon="ant-design:download-outlined" /> 下载
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </Spin>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getProductDetail } from '/@/api/crmfy/crmProducts.api';
  import { Icon } from '/@/components/Icon';
  import { Spin } from 'ant-design-vue';
  import { ProductDetail, ProductFile } from '/@/types/crmfy/crmProducts';
  import { getDictItemsByCode } from '/@/utils/dict';

  // 加载状态
  const loading = ref<boolean>(false);

  // 产品详情数据
  const productDetail = reactive<ProductDetail>({
    id: '',
    productCode: '',
    productName: '',
    planName: '',
    hot: false,
    categoryCode: '',
    categoryCode_dictText: '',
    companyCode: '',
    companyCode_dictText: '',
    desc: '',
    currencies: [],
    frequencies: [],
    paymentTerms: [],
    files: [],
  });

  // 缴费年期表格列定义
  const paymentTermColumns = [
    {
      title: '缴费年期',
      dataIndex: 'paymentTerm',
      width: '50%',
    },
    {
      title: '合约手续费率（按年份）',
      dataIndex: 'contractRates',
      width: '50%',
    },
  ];

  // 文件表格列定义
  const fileColumns = [
    {
      title: '文件名称',
      dataIndex: 'fileName',
      width: '40%',
    },
    {
      title: '文件类型',
      dataIndex: 'fileType',
      width: '20%',
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      width: '20%',
      customRender: ({ text }) => {
        return text ? formatFileSize(text) : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '20%',
    },
  ];

  // 注册modal
  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    try {
      loading.value = true;
      setModalProps({ confirmLoading: false });

      // 获取产品详情
      const detail = await getProductDetail(data.record.id);

      // 更新产品详情数据
      Object.assign(productDetail, detail);
    } catch (error) {
      console.error('获取产品详情失败', error);
    } finally {
      loading.value = false;
    }
  });

  // 获取币种标签
  function getCurrencyLabel(value: string): string {

    // 从字典中获取缴费频率标签
    const dictItems = getDictItemsByCode('payment_currency');
    if (dictItems && dictItems.length > 0) {
      const item = dictItems.find(item => item.value === value);
      return item ? item.text : value;
    }
    // 如果字典未加载，使用默认映射
    const currencyMap = {
      'CNY': '人民币',
      'USD': '美元',
      'HK': '港币',
    };
    return currencyMap[value] || value;
  }

  // 获取缴费频率标签
  function getFrequencyLabel(value: string): string {
    // 从字典中获取缴费频率标签
    const dictItems = getDictItemsByCode('payment_frequency');
    if (dictItems && dictItems.length > 0) {
      const item = dictItems.find(item => item.value === value);
      return item ? item.text : value;
    }
    // 如果字典未加载，使用默认映射
    const frequencyMap = {
      '1': '月缴',
      '2': '季缴',
      '3': '半年缴',
      '4': '年缴',
      '5': '趸交',
    };
    return frequencyMap[value] || value;
  }

  // 格式化文件大小
  function formatFileSize(size: number): string {
    if (size < 1024) {
      return size + ' B';
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' KB';
    } else if (size < 1024 * 1024 * 1024) {
      return (size / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
      return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
  }

  // 下载文件
  function downloadFile(file: ProductFile) {
    if (file.filePath) {
      window.open(file.filePath);
    }
  }
</script>

<style lang="less" scoped>
.detail-section {
  margin-top: 24px;

  h3 {
    margin-bottom: 16px;
    font-weight: 500;
  }
}

.contract-rate-item {
  padding: 4px 0;
  display: flex;
  align-items: center;

  &:not(:last-child) {
    border-bottom: 1px dashed #f0f0f0;
    margin-bottom: 4px;
  }

  .year-label {
    min-width: 70px;
    color: rgba(0, 0, 0, 0.65);
  }

  .rate-value {
    font-weight: 500;
    color: #1890ff;
  }
}
</style>



















