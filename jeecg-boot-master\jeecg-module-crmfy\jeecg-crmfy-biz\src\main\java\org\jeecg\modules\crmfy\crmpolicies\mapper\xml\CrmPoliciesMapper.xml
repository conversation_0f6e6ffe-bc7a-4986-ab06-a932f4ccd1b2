<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crmfy.crmpolicies.mapper.CrmPoliciesMapper">

    <!-- 分页查询保单信息 -->
    <select id="queryPoliciesInfo" resultType="org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesVO">
        SELECT 
            p.*,
            c.customer_name as customerName
        FROM crm_policies p left join crm_customer c on p.customer_id = c.id
        WHERE p.del_flag = 0 
        
        <if test="query.izActive != null and query.izActive != ''">
            and p.iz_active =  #{query.izActive}
        </if>
         
        <if test="query.supplierNo != null and query.supplierNo != ''">
            AND p.supplier_no = #{query.supplierNo}
        </if>
        <if test="query.productName != null and query.productName != ''">
            AND p.product_name LIKE CONCAT('%',#{query.productName},'%')
        </if>
        <if test="query.planName != null and query.planName != ''">
            AND p.plan_name LIKE CONCAT('%',#{query.planName},'%')
        </if>
        <if test="query.customerId != null">
            AND p.customer_id = #{query.customerId}
        </if>
        <if test="query.salesUsername != null and query.salesUsername != ''">
            AND p.sales_username = #{query.salesUsername}
        </if>
        <if test="query.policyNo != null and query.policyNo != ''">
            AND p.policy_no = #{query.policyNo}
        </if>
        <if test="query.policyStatus != null and query.policyStatus != ''">
            AND p.policy_status = #{query.policyStatus}
        </if>
        <if test="query.orgCode != null and query.orgCode != ''">
            AND p.org_code LIKE CONCAT(#{query.orgCode},'%') 
        </if>
        ORDER BY p.create_time DESC
    </select>

</mapper>