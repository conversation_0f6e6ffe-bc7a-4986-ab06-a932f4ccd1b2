<template>
  <div>
    <!-- 引用表格 -->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!-- 插槽:table标题 -->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
        <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
      </template>

      <!-- 公司名称列自定义渲染 -->
      <template #companyCode="{ text }">
        <a-tag color="blue">{{ getDictText('supplier', text) }}</a-tag>
      </template>

      <!-- 产品类型列自定义渲染 -->
      <template #categoryCode="{ text }">
        <a-tag color="blue">{{ getDictText('product_category', text) }}</a-tag>
      </template>

      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单弹窗 -->
    <CrmProductsModal @register="registerModal" @success="handleSuccess" />

    <!-- 详情弹窗 -->
    <CrmProductsDetail @register="registerDetailModal" />
  </div>
</template>

<script lang="ts" name="crmfy-crmProducts" setup>
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import CrmProductsModal from './modules/CrmProductsModal.vue';
  import CrmProductsDetail from './modules/CrmProductsDetail.vue';
  import { columns, searchFormSchema } from './CrmProducts.data';
  import { list, deleteOne, batchDelete, getExportUrl, getImportUrl } from '/@/api/crmfy/crmProducts.api';
  import { Icon } from '/@/components/Icon';
  import { getDictItemsByCode } from '/@/utils/dict/index';
  import { Tag } from 'ant-design-vue';

  // 注册modal
  const [registerModal, { openModal }] = useModal();
  const [registerDetailModal, { openModal: openDetailModal }] = useModal();

  // 注册table数据
  const { tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '产品信息管理',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
      },
      actionColumn: {
        width: 120,
      },
    },
    exportConfig: {
      name: "产品信息管理",
      url: getExportUrl,
    },
    importConfig: {
      url: getImportUrl
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 字典翻译函数
  const getDictText = (dictCode: string, value: string | number | undefined) => {
    if (!value || !dictCode) return value || '';
    const dictItems = getDictItemsByCode(dictCode);
    if (dictItems && dictItems.length > 0) {
      const item = dictItems.find((item: any) => item.value == value);
      return item ? item.text : value;
    }
    return value;
  };

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openDetailModal(true, {
      record,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteOne({ id: record.id }, reload);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    batchDelete({ ids: selectedRowKeys.value }, reload);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record: Recordable) {
    return [

      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        }
      }
    ];
  }
</script>

<style scoped></style>



