import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

// 列表页表格列定义
export const columns: BasicColumn[] = [
  {
    title: '公司名称',
    dataIndex: 'companyCode_dictText',
    width: 120,
    slots: { customRender: 'companyCode_dictText' },
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    width: 180,
  },
  {
    title: '产品名称英文',
    dataIndex: 'planName',
    width: 180,
  },
  {
    title: '产品类型',
    dataIndex: 'categoryCode',
    width: 120,
    slots: { customRender: 'categoryCode' },
  },
  {
    title: '是否热门产品',
    dataIndex: 'hot',
    width: 100,
    customRender: ({ record }) => {
      const hot = record.hot;
      const color = hot ? 'red' : 'default';
      const text = hot ? '是' : '否';
      return h(Tag, { color: color }, () => text);
    },
  },
];

// 搜索表单定义
export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyWords',
    label: '关键字搜索',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '产品名称、公司名称、分类名称等',
    },
  },
  {
    field: 'categoryCode',
    label: '产品类型',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      dictCode: 'product_category',
      placeholder: '请选择产品类型',
    },
  },
  {
    field: 'companyCode',
    label: '公司',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      dictCode: 'supplier',
      placeholder: '请选择公司',
    },
  },
  {
    field: 'hot',
    label: '是否热门产品',
    component: 'Switch',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '是否热门产品',
    },
  },
];

// 产品表单定义
export const productFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'productCode',
    label: '产品唯一编码',
    component: 'Input',
    show: false,
  },
  {
    field: 'companyCode',
    label: '公司名称',
    component: 'JDictSelectTag',
    required: true,
    componentProps: {
      dictCode: 'supplier',
      placeholder: '请选择公司',
    },
  },
  {
    field: 'productName',
    label: '产品名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入产品名称',
    },
  },
  {
    field: 'planName',
    label: '产品名称英文',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入产品名称英文',
    },
  },
  {
    field: 'categoryCode',
    label: '产品类型',
    component: 'JDictSelectTag',
    required: true,
    componentProps: {
      dictCode: 'product_category',
      placeholder: '请选择产品类型',
    },
  },
  {
    field: 'hot',
    label: '是否热门产品',
    component: 'Switch',
    required: true,
    defaultValue: false,
  },


  {
    field: 'currencies',
    label: '支持币种',
    component: 'JSelectMultiple',
    required: true,
    componentProps: {
      dictCode: 'payment_currency',
      placeholder: '请选择支持币种',
    },
  },
  {
    field: 'frequencies',
    label: '缴费频率',
    component: 'JSelectMultiple',
    required: true,
    componentProps: {
     dictCode: 'payment_frequency',
      placeholder: '请选择缴费频率',
    },
  },
  {
    field: 'productStatus',
    label: '产品状态',
    component: 'JDictSelectTag',
    required: true,
    componentProps: {
     dictCode: 'product_status',
      placeholder: '请选择产品状态',
    },
    defaultValue: '1',
  },
  {
    field: 'desc',
    label: '产品描述',
    component: 'JEditor',
    required: true,
    colProps: { span: 24 }, // 设置为24使其占据整行

    componentProps: {
      height: 200,
    },
  },
  {
    field: 'paymentTermsDisplay', // 使用不同的字段名，避免与实际数据冲突
    label: '缴费年期',
    component: 'Input',
    slot: 'paymentTerms',
    required: false,
    rules: [], // 完全移除验证规则
    show: true, // 确保显示标签和插槽
    componentProps: {
      style: { display: 'none' }, // 隐藏输入框，只使用自定义插槽
    },
  },
  {
    field: 'files',
    label: '产品文件',
    component: 'JUpload',
    slot: 'files',
  },
];
