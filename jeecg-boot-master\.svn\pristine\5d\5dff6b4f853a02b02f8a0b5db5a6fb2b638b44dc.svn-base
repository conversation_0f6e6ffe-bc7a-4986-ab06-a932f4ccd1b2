package org.jeecg.modules.crmfy.crmpolicies.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.CommonSendStatus;
import org.jeecg.common.constant.WebsocketConst;
import org.jeecg.common.desensitization.annotation.SensitiveEncode;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.crmfy.crmcommissioncalculation.entity.CrmCommissionCalculation;
import org.jeecg.modules.crmfy.crmcommissioncalculation.mapper.CrmCommissionCalculationMapper;
import org.jeecg.modules.crmfy.crmcommissioncalculation.service.ICrmCommissionCalculationService;
import org.jeecg.modules.crmfy.crmcommissioncalculation.service.impl.CrmCommissionCalculationServiceImpl;
import org.jeecg.modules.crmfy.crmpolicies.entity.CrmPolicies;
import org.jeecg.modules.crmfy.crmpolicies.mapper.CrmPoliciesMapper;
import org.jeecg.modules.crmfy.crmpolicies.service.ICrmPoliciesService;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesDTO;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesPO;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesVO;
import org.jeecg.modules.crmfy.crmpolicyadditional.entity.CrmPolicyAdditional;
import org.jeecg.modules.crmfy.crmpolicyadditional.mapper.CrmPolicyAdditionalMapper;
import org.jeecg.modules.crmfy.crmpolicybeneficiaries.entity.CrmPolicyBeneficiaries;
import org.jeecg.modules.crmfy.crmpolicybeneficiaries.mapper.CrmPolicyBeneficiariesMapper;
import org.jeecg.modules.crmfy.crmpolicyholders.entity.CrmPolicyHolders;
import org.jeecg.modules.crmfy.crmpolicyholders.mapper.CrmPolicyHoldersMapper;
import org.jeecg.modules.crmfy.crmpolicyinsureds.entity.CrmPolicyInsureds;
import org.jeecg.modules.crmfy.crmpolicyinsureds.mapper.CrmPolicyInsuredsMapper;
import org.jeecg.modules.crmfy.crmpolicyversions.entity.CrmPolicyVersions;
import org.jeecg.modules.crmfy.crmpolicyversions.mapper.CrmPolicyVersionsMapper;
import org.jeecg.modules.crmfy.crmproductcontractrate.entity.CrmProductContractRate;
import org.jeecg.modules.crmfy.crmproductcontractrate.mapper.CrmProductContractRateMapper;
import org.jeecg.modules.crmfy.enums.PolicyStatusEnum;
import org.jeecg.modules.jsjx.enums.PostEnum;
import org.jeecg.modules.jsjx.utils.DataUtils;
import org.jeecg.modules.jsjx.utils.MyDateUtils;
import org.jeecg.modules.message.websocket.WebSocket;
import org.jeecg.modules.system.entity.SysAnnouncement;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysAnnouncementService;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 保单信息管理
 * @Author: jeecg-boot
 * @Date: 2025-03-28
 * @Version: V1.0
 */
@Service
public class CrmPoliciesServiceImpl extends ServiceImpl<CrmPoliciesMapper, CrmPolicies> implements ICrmPoliciesService {

	@Autowired
	private CrmPolicyHoldersMapper policyHoldersMapper;

	@Autowired
	private CrmPolicyInsuredsMapper policyInsuredsMapper;

	@Autowired
	private CrmPolicyBeneficiariesMapper policyBeneficiariesMapper;

	@Autowired
	private CrmPolicyVersionsMapper policyVersionsMapper;

	@Autowired
	private ISysUserService sysUserService;

	@Autowired
	private ISysAnnouncementService sysAnnouncementService;

	@Autowired
	private CrmPolicyAdditionalMapper crmPolicyAdditionalMapper;

	@Autowired
	private CrmProductContractRateMapper crmProductContractRateMapper;
	
    @Autowired
	private CrmCommissionCalculationMapper crmCommissionCalculationMapper;

	@Autowired
	private ICrmCommissionCalculationService crmCommissionCalculationService;


	@Resource
	private WebSocket webSocket;

	@SensitiveEncode
	@Override
	public CrmPoliciesDTO queryCrmPoliciesByIdEncode(String id) {
		if (StringUtils.isBlank(id)) {
			throw new JeecgBootException("参数错误");
		}

		// 查询保单主表信息
		CrmPolicies crmPolicies = this.getById(id);
		if (crmPolicies == null) {
			throw new JeecgBootException("保单不存在");
		}

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();

		if (post.equals(PostEnum.XSKHJL.getCode()) && !loginUser.getUsername().equals(crmPolicies.getSalesUsername())) {// 销售客户经理
			throw new JeecgBootException("非法操作，无权限！");
		}

		// 创建返回对象
		CrmPoliciesDTO crmPoliciesDTO = new CrmPoliciesDTO();
		BeanUtils.copyProperties(crmPolicies, crmPoliciesDTO);

		// 查询投保人信息
		CrmPolicyHolders policyHolders = policyHoldersMapper.selectOne(
				new QueryWrapper<CrmPolicyHolders>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyHolders(policyHolders);

		// 查询被保人信息
		CrmPolicyInsureds policyInsureds = policyInsuredsMapper.selectOne(
				new QueryWrapper<CrmPolicyInsureds>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyInsureds(policyInsureds);

		// 查询受益人信息
		CrmPolicyBeneficiaries policyBeneficiaries = policyBeneficiariesMapper.selectOne(
				new QueryWrapper<CrmPolicyBeneficiaries>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyBeneficiaries(policyBeneficiaries);

		// 查询附加险信息
		List<CrmPolicyAdditional> crmPolicyAdditionals = crmPolicyAdditionalMapper.selectList(
				new QueryWrapper<CrmPolicyAdditional>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyAdditionals(crmPolicyAdditionals);
		return crmPoliciesDTO;
	}

	@Override
	public CrmPoliciesDTO queryCrmPoliciesById(String id) {
		if (StringUtils.isBlank(id)) {
			throw new JeecgBootException("参数错误");
		}

		// 查询保单主表信息
		CrmPolicies crmPolicies = this.getById(id);
		if (crmPolicies == null) {
			throw new JeecgBootException("保单不存在");
		}
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();

		if (post.equals(PostEnum.XSKHJL.getCode()) && !loginUser.getUsername().equals(crmPolicies.getSalesUsername())) {// 销售客户经理
			throw new JeecgBootException("非法操作，无权限！");
		}
		// 创建返回对象
		CrmPoliciesDTO crmPoliciesDTO = new CrmPoliciesDTO();
		BeanUtils.copyProperties(crmPolicies, crmPoliciesDTO);

		// 查询投保人信息
		CrmPolicyHolders policyHolders = policyHoldersMapper.selectOne(
				new QueryWrapper<CrmPolicyHolders>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyHolders(policyHolders);

		// 查询被保人信息
		CrmPolicyInsureds policyInsureds = policyInsuredsMapper.selectOne(
				new QueryWrapper<CrmPolicyInsureds>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyInsureds(policyInsureds);

		// 查询受益人信息
		CrmPolicyBeneficiaries policyBeneficiaries = policyBeneficiariesMapper.selectOne(
				new QueryWrapper<CrmPolicyBeneficiaries>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyBeneficiaries(policyBeneficiaries);

		// 查询附加险信息
		List<CrmPolicyAdditional> crmPolicyAdditionals = crmPolicyAdditionalMapper.selectList(
				new QueryWrapper<CrmPolicyAdditional>()
						.eq("policy_id", id)
						.eq("version", crmPolicies.getCurrentVersion())
						.eq("del_flag", 0));
		crmPoliciesDTO.setCrmPolicyAdditionals(crmPolicyAdditionals);

		return crmPoliciesDTO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveCrmPolicies(CrmPoliciesDTO crmPoliciesDTO) {

		// 1、根据CrmPoliciesDTO 保存主表CrmPolicies，2、根据CrmPoliciesDTO
		// 保存子表CrmPolicyHolders，3、根据CrmPoliciesDTO
		// 保存子表CrmPolicyInsureds，4、根据CrmPoliciesDTO 保存子表CrmPolicyBeneficiaries
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 1. 保存主表CrmPolicies
		CrmPolicies crmPolicies = new CrmPolicies();
		// 设置主表数据
		crmPolicies.setSupplierNo(crmPoliciesDTO.getSupplierNo());
		crmPolicies.setProductName(crmPoliciesDTO.getProductName());
		crmPolicies.setProductId(crmPoliciesDTO.getProductId());
		crmPolicies.setPaymentTerm(crmPoliciesDTO.getPaymentTerm());
		crmPolicies.setPaymentFrequency(crmPoliciesDTO.getPaymentFrequency());
		crmPolicies.setPaymentCurrency(crmPoliciesDTO.getPaymentCurrency());
		crmPolicies.setPaymentMethod(crmPoliciesDTO.getPaymentMethod());
		crmPolicies.setPaymentAmount(crmPoliciesDTO.getPaymentAmount());
		crmPolicies.setPolicyNo(crmPoliciesDTO.getPolicyNo());
		crmPolicies.setCoveragePeriod(crmPoliciesDTO.getCoveragePeriod());
		crmPolicies.setAutoDebit(crmPoliciesDTO.getAutoDebit());
		crmPolicies.setPromotionPlan(crmPoliciesDTO.getPromotionPlan());
		crmPolicies.setNotificationEmail(crmPoliciesDTO.getNotificationEmail());
		crmPolicies.setSignatureSms(crmPoliciesDTO.getSignatureSms());
		crmPolicies.setCurrentVersion(1); // 初始版本为1
		crmPolicies.setCustomerId(crmPoliciesDTO.getCustomerId());
		crmPolicies.setDelFlag(0);
		crmPolicies.setSalesUsername(loginUser.getUsername());
		crmPolicies.setOrgCode(loginUser.getOrgCode());
		CrmPolicyBeneficiaries crmPolicyBeneficiaries = crmPoliciesDTO.getCrmPolicyBeneficiaries();
		CrmPolicyInsureds crmPolicyInsureds = crmPoliciesDTO.getCrmPolicyInsureds();
		CrmPolicyHolders crmPolicyHolders = crmPoliciesDTO.getCrmPolicyHolders();
		List<CrmPolicyAdditional>  crmPolicyAdditionals   = crmPoliciesDTO.getCrmPolicyAdditionals();
		crmPolicies.setPolicyUuid(java.util.UUID.randomUUID().toString());

		if (crmPoliciesDTO.getIzSubmit().equals("0")) {
			crmPolicies.setNextNode("1");// 提交
			crmPolicies.setPolicyStatus(PolicyStatusEnum.SIGNED_SAVE.getStatus());// 草稿
		} else {
			crmPolicies.setNextNode("2");// 运营审核
			crmPolicies.setPolicyStatus(PolicyStatusEnum.SIGNED_PENDING_SUBMIT.getStatus());// 已签单待提交
		}
		// 保存主表数据
		this.save(crmPolicies);

		// 获取保存后的主表ID
		Integer policyId = crmPolicies.getId();

		// 2. 保存子表CrmPolicyHolders（投保人信息）
		if (crmPolicyHolders != null) {
			// 设置关联的保单ID和版本号
			crmPolicyHolders.setPolicyId(policyId);
			crmPolicyHolders.setVersion(1); // 初始版本为1
			crmPolicyHolders.setDelFlag(0);
			// 保存投保人信息
			policyHoldersMapper.insert(crmPolicyHolders);
		}

		// 3. 保存子表CrmPolicyInsureds（被保人信息）
		if (crmPolicyInsureds != null) {
			// 设置关联的保单ID和版本号
			crmPolicyInsureds.setPolicyId(policyId);
			crmPolicyInsureds.setVersion(1); // 初始版本为1
			crmPolicyInsureds.setDelFlag(0); // 设置删除标记为0，未删除状态，默认值为0，可根据需求进行修改或删除该属性或注释掉这行代码
			// 保存被保人信息
			policyInsuredsMapper.insert(crmPolicyInsureds);
		}

		// 4. 保存子表CrmPolicyBeneficiaries（受益人信息）
		if (crmPolicyBeneficiaries != null) {
			// 设置关联的保单ID和版本号
			crmPolicyBeneficiaries.setPolicyId(policyId);
			crmPolicyBeneficiaries.setVersion(1); // 初始版本为1
			crmPolicyBeneficiaries.setDelFlag(0); // 设置删除标记为0，未删除状态，默认值为0，可根据需求进行修改或删除该属性或注释掉这行代码
			// 保存受益人信息
			policyBeneficiariesMapper.insert(crmPolicyBeneficiaries);
		}

		//保存附加险信息
		if(crmPolicyAdditionals != null&&crmPolicyAdditionals.size()>0) {
			for(CrmPolicyAdditional crmPolicyAdditional:crmPolicyAdditionals) {
				crmPolicyAdditional.setPolicyId(policyId);
				crmPolicyAdditional.setVersion(1); // 初始版本为1
				crmPolicyAdditional.setDelFlag(0);
				crmPolicyAdditionalMapper.insert(crmPolicyAdditional);
			}
		}	

		// 5. 创建保单版本记录
		CrmPolicyVersions crmPolicyVersions = new CrmPolicyVersions();
		crmPolicyVersions.setPolicyId(policyId);
		crmPolicyVersions.setVersion(1); // 初始版本为1
		crmPolicyVersions.setHolderId(crmPolicyHolders != null ? crmPolicyHolders.getId() : null);
		crmPolicyVersions.setInsuredId(crmPolicyInsureds != null ? crmPolicyInsureds.getId() : null);
		crmPolicyVersions.setBeneficiaryId(crmPolicyBeneficiaries != null ? crmPolicyBeneficiaries.getId() : null);
		crmPolicyVersions.setEffectiveDate(new Date()); // 设置当前日期为生效日期
		crmPolicyVersions.setDelFlag(0);
		crmPolicyVersions.setPolicyUuid(crmPolicies.getPolicyUuid());
		crmPolicyVersions.setIzActive("1");
		// 保存版本记录
		policyVersionsMapper.insert(crmPolicyVersions);

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateCrmPoliciesByIdSales(CrmPoliciesDTO crmPoliciesDTO) {
		if (crmPoliciesDTO == null || crmPoliciesDTO.getId() == null) {
			throw new JeecgBootException("参数错误");
		}
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();
		if (StringUtils.isBlank(post)) {
			throw new JeecgBootException("操作失败，请先分配职务");
		}
		// 1. 获取当前保单信息
		CrmPolicies currentPolicy = this.getById(crmPoliciesDTO.getId());
		if (currentPolicy == null) {
			throw new JeecgBootException("保单不存在");
		}

		if (post.equals(PostEnum.XSKHJL.getCode())) {// 销售客户经理

			if (!loginUser.getUsername().equals(currentPolicy.getSalesUsername())) {// 销售客户经理只能修改自己的保单
				throw new JeecgBootException("非法操作，无权限！");
			}

			if (!currentPolicy.getNextNode().equals("1") && !currentPolicy.getNextNode().equals("4")) {// 下个节点是提交/照会回复
				throw new JeecgBootException("当前保单状态不允许修改");
			}

			CrmPolicies crmPolicies = new CrmPolicies();
			BeanUtils.copyProperties(crmPoliciesDTO, crmPolicies);
			crmPolicies.setId(crmPoliciesDTO.getId());
			crmPolicies.setUpdateBy(loginUser.getUsername());
			crmPolicies.setUpdateTime(new Date());

			if (currentPolicy.getNextNode().equals("1")) {// 下个节点是提交

				if (crmPoliciesDTO.getIzSubmit().equals("1")) {
					crmPolicies.setNextNode("2");// 运营审核
					crmPolicies.setPolicyStatus(PolicyStatusEnum.SIGNED_PENDING_SUBMIT.getStatus());// 已签单待提交
				}

			} else {// 下个节点是照会回复
				currentPolicy.setNoticeReply(crmPoliciesDTO.getNoticeReply());
				crmPolicies.setNextNode("3");// 核保回复
				crmPolicies.setPolicyStatus(PolicyStatusEnum.INQUIRY_REPLIED_PENDING_UNDERWRITING.getStatus());// 照会已回复待核保
			}

			CrmPolicyBeneficiaries crmPolicyBeneficiaries = crmPoliciesDTO.getCrmPolicyBeneficiaries();
			CrmPolicyInsureds crmPolicyInsureds = crmPoliciesDTO.getCrmPolicyInsureds();
			CrmPolicyHolders crmPolicyHolders = crmPoliciesDTO.getCrmPolicyHolders();
			List<CrmPolicyAdditional>  crmPolicyAdditionals   = crmPoliciesDTO.getCrmPolicyAdditionals();
			// 更新主表数据
			this.updateById(crmPolicies);

			// 2. 修改子表CrmPolicyHolders（投保人信息）
			if (crmPolicyHolders != null) {
				// 修改投保人信息
				policyHoldersMapper.updateById(crmPolicyHolders);
			}

			// 3. 修改子表CrmPolicyInsureds（被保人信息）
			if (crmPolicyInsureds != null) {
				// 修改被保人信息
				policyInsuredsMapper.updateById(crmPolicyInsureds);
			}

			// 4. 修改子表CrmPolicyBeneficiaries（受益人信息）
			if (crmPolicyBeneficiaries != null) {
				// 修改受益人信息
				policyBeneficiariesMapper.updateById(crmPolicyBeneficiaries);
			}

			//修改附加险信息
			if(crmPolicyAdditionals != null&&crmPolicyAdditionals.size()>0) {
				//先删除 后新增
				//删除原附加信息
				
				crmPolicyAdditionalMapper.delete(new LambdaQueryWrapper<CrmPolicyAdditional>().eq(CrmPolicyAdditional::getPolicyId, crmPoliciesDTO.getId()));
				for(CrmPolicyAdditional crmPolicyAdditional:crmPolicyAdditionals) {
					crmPolicyAdditional.setVersion(currentPolicy.getCurrentVersion()); 
					crmPolicyAdditional.setDelFlag(0);
					crmPolicyAdditional.setPolicyId(crmPoliciesDTO.getId());
					crmPolicyAdditional.setId(null);
					crmPolicyAdditionalMapper.insert(crmPolicyAdditional);
				}
			}


		} else {
			throw new JeecgBootException("当前用户不允许操作");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateCrmPoliciesById(CrmPoliciesDTO crmPoliciesDTO) {

		if (crmPoliciesDTO == null || crmPoliciesDTO.getId() == null) {
			throw new JeecgBootException("参数错误");
		}

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();
		if (StringUtils.isBlank(post)) {
			throw new JeecgBootException("操作失败，请先分配职务");

		}

		// 1. 获取当前保单信息
		CrmPolicies currentPolicy = this.getById(crmPoliciesDTO.getId());
		if (currentPolicy == null) {
			throw new JeecgBootException("保单不存在");
		}

		if (post.equals(PostEnum.YYRY.getCode())) {// 运营岗
			// if (post.equals(PostEnum.XSKHJL.getCode())
			// &&
			// !PolicyStatusEnum.SIGNED_SAVE.getStatus().equals(currentPolicy.getPolicyStatus()))
			// {// 销售客户经理
			// throw new JeecgBootException("当前保单状态不允许修改");
			// }

			CrmPolicyBeneficiaries crmPolicyBeneficiaries = crmPoliciesDTO.getCrmPolicyBeneficiaries();
			CrmPolicyInsureds crmPolicyInsureds = crmPoliciesDTO.getCrmPolicyInsureds();
			CrmPolicyHolders crmPolicyHolders = crmPoliciesDTO.getCrmPolicyHolders();
			List<CrmPolicyAdditional>  crmPolicyAdditionals   = crmPoliciesDTO.getCrmPolicyAdditionals();

			// 3. 更新主表CrmPolicies数据
			CrmPolicies crmPolicies = new CrmPolicies();
			BeanUtils.copyProperties(crmPoliciesDTO, crmPolicies);
			crmPolicies.setId(crmPoliciesDTO.getId());
			if (!PolicyStatusEnum.ORDER_COMPLETED.getStatus().equals(currentPolicy.getPolicyStatus())) {// 订单完成前可直接修改

				if (currentPolicy.getNextNode().equals("2")) {// 下个节点是运营岗审核提交

					crmPolicies.setPolicyStatus(PolicyStatusEnum.SUBMITTED_PENDING_UNDERWRITING.getStatus());// 已提交待核保
					crmPolicies.setNextNode("3");// 核保回复

				} else if (currentPolicy.getNextNode().equals("3")) {// 下个节点是运营岗核保回复

					if (crmPoliciesDTO.getAuditResult().equals("1")) {//核保通过
						crmPolicies.setNextNode("5");// 保费到账
						crmPolicies.setPolicyStatus(PolicyStatusEnum.UNDERWRITTEN_PENDING_PAYMENT.getStatus());// 核保通过待保费到账
					} else {//核保不通过
						crmPolicies.setNextNode("4");// 照会回复
						crmPolicies.setPolicyStatus(PolicyStatusEnum.INQUIRY_PENDING_PROCESSING.getStatus());// 照会待处理
						crmPolicies.setNoticeRemark(crmPoliciesDTO.getNoticeRemark());
						
						// 发送通知给销售客户经理
						SysAnnouncement sysAnnouncement = new SysAnnouncement();
						sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);
						sysAnnouncement.setSendTime(new Date());
						sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
						sysAnnouncement.setSender(loginUser.getUsername());
						sysAnnouncement.setTitile("照会通知");
						sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);
						// 通过username获取userId
						SysUser user = sysUserService.getUserByName(currentPolicy.getSalesUsername());
						sysAnnouncement.setUserIds(user.getId());
						sysAnnouncement
								.setMsgContent("尊敬的客户：您的核保未通过，" + crmPolicies.getNoticeRemark() + "，请按照相关规定进行处理。");
						sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);
						sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);
						sysAnnouncement.setMsgAbstract("照会通知");

						// 保存系统公告
						sysAnnouncementService.saveAnnouncement(sysAnnouncement);
						// 发送webSocket通知
						String userId = sysAnnouncement.getUserIds();
						// String[] userIds = userId.substring(0, (userId.length() - 1)).split(",");
						JSONObject obj = new JSONObject();
						obj.put(WebsocketConst.MSG_CMD, WebsocketConst.CMD_USER);
						obj.put(WebsocketConst.MSG_ID, sysAnnouncement.getId());
						obj.put(WebsocketConst.MSG_TXT, sysAnnouncement.getTitile());
						webSocket.sendMessage(userId, obj.toJSONString());
					}

				} else if (currentPolicy.getNextNode().equals("4")) {// 下个节点是照会回复
					currentPolicy.setNoticeReply(crmPoliciesDTO.getNoticeReply());
					crmPolicies.setNextNode("3");// 核保回复
					crmPolicies.setPolicyStatus(PolicyStatusEnum.INQUIRY_REPLIED_PENDING_UNDERWRITING.getStatus());// 照会已回复待核保

				} else if (currentPolicy.getNextNode().equals("5")) {// 下个节点是保费到账确认
					crmPolicies.setNextNode("6");// 保单签发
					crmPolicies.setPolicyStatus(PolicyStatusEnum.PAID_PENDING_ISSUANCE.getStatus());// 保费到账待签发保单

				} else if (currentPolicy.getNextNode().equals("6")) {// 下个节点是保单签发
					crmPolicies.setNextNode("7");// 保单签发
					crmPolicies.setPolicyStatus(PolicyStatusEnum.POLICY_ISSUED.getStatus());// 保单签发
					Date date = new Date();
					crmPolicies.setPolicyIssueDate(date);// 保单签发日期
					crmPolicies.setCoolingOffDate(MyDateUtils.getExpectDate(date, 21));// 保单生效日期
					// todo 发送短信/通知 签名/邮件
				} else if (currentPolicy.getNextNode().equals("7")) {// 下个节点是保单完成
					if (currentPolicy.getCoolingOffDate().compareTo(new Date()) < 0) {
						throw new JeecgBootException("保单尚未过冷静期，不能完成！");
					}
					crmPolicies.setNextNode("8");// 保单流程结束
					crmPolicies.setPolicyStatus(PolicyStatusEnum.ORDER_COMPLETED.getStatus());// 保单完成
					//计算保单每期佣金
					//获取销售员佣金费率
					BigDecimal commissionRate = sysUserService.getUserByName(currentPolicy.getSalesUsername()).getCommissionRate();
					if(commissionRate==null) {
						throw new JeecgBootException("请先设置销售员佣金费率！");
					}
					//获取保单对应合约手续费率
					List<CrmProductContractRate> crmProductContractRates = crmProductContractRateMapper.selectList(new LambdaQueryWrapper<CrmProductContractRate>().
					eq(CrmProductContractRate::getProductId, currentPolicy.getProductId()).
					eq(CrmProductContractRate::getPaymentTerm, currentPolicy.getPaymentTerm()).orderByAsc(CrmProductContractRate::getYearSeq)); 
					Date date = new Date();
					if(crmProductContractRates.size()>0) {
						List<CrmCommissionCalculation> crmCommissionCalculations = new ArrayList<CrmCommissionCalculation>();
						//遍历
						for(CrmProductContractRate crmProductContractRate:crmProductContractRates) {
							//计算保单每期主险佣金
							BigDecimal commissionAmount = BigDecimal.ZERO;
							if(currentPolicy.getPaymentTerm()<5) {
								commissionAmount = currentPolicy.getPaymentAmount().multiply(crmProductContractRate.getContractRate().divide(new BigDecimal(100)).subtract(commissionRate)).setScale(2,BigDecimal.ROUND_HALF_UP);   
							}else {
								commissionAmount = currentPolicy.getPaymentAmount().multiply(crmProductContractRate.getContractRate()).divide(new BigDecimal(100)).multiply(commissionRate).setScale(2,BigDecimal.ROUND_HALF_UP);  
							}
							//保存佣金到佣金计算表
							CrmCommissionCalculation crmCommissionCalculation = new CrmCommissionCalculation();
							crmCommissionCalculation.setPolicyId(currentPolicy.getId());
							crmCommissionCalculation.setPlanName(currentPolicy.getPlanName());
							crmCommissionCalculation.setPolicyNo(crmPoliciesDTO.getPolicyNo());	
							crmCommissionCalculation.setRateId(crmProductContractRate.getId());	
							crmCommissionCalculation.setYearNum(crmProductContractRate.getYearSeq());		
							crmCommissionCalculation.setPremiumAmount(currentPolicy.getPaymentAmount());		
							crmCommissionCalculation.setCommissionRate(commissionRate);	
							crmCommissionCalculation.setCommissionAmount(commissionAmount);
							crmCommissionCalculation.setCompanyPayoutRatio(crmProductContractRate.getContractRate());
							crmCommissionCalculation.setContractRate(crmProductContractRate.getContractRate());
							//crmCommissionCalculation.setCompanyFee(crmProductContractRate.getCompanyFee());
							crmCommissionCalculation.setPaymentCurrency(currentPolicy.getPaymentCurrency());
							crmCommissionCalculation.setCalculationDate(date);
							crmCommissionCalculation.setPayoutStatus("0");
							crmCommissionCalculation.setSysOrgCode(loginUser.getOrgCode());
							crmCommissionCalculation.setSaleName(currentPolicy.getSalesUsername());
							crmCommissionCalculation.setDelFlag(0);
							crmCommissionCalculation.setPayoutDate(MyDateUtils.getExpectYear(date, crmProductContractRate.getYearSeq()-1));


							crmCommissionCalculations.add(crmCommissionCalculation);	
							
						}
						
						
						//计算每期附加险佣金
						List<CrmPolicyAdditional> additionals = crmPolicyAdditionalMapper.selectList(new LambdaQueryWrapper<CrmPolicyAdditional>().eq(CrmPolicyAdditional::getPolicyId, currentPolicy.getId()));
						if(additionals.size()>0) {
							for(CrmPolicyAdditional crmPolicyAdditional:additionals) {	
								
								//获取保单对应合约手续费率
								List<CrmProductContractRate> crmProductContractRates2 = crmProductContractRateMapper.selectList(new LambdaQueryWrapper<CrmProductContractRate>().
								eq(CrmProductContractRate::getProductId, crmPolicyAdditional.getProductId()).
								eq(CrmProductContractRate::getPaymentTerm, currentPolicy.getPaymentTerm()).orderByAsc(CrmProductContractRate::getYearSeq)); 
								
								for(CrmProductContractRate crmProductContractRate2:crmProductContractRates2) {
									//计算每期附加险佣金
									BigDecimal additionalCommissionAmount = BigDecimal.ZERO;
									if(currentPolicy.getPaymentTerm()<5) {
										additionalCommissionAmount = crmPolicyAdditional.getAdditionalAmount().multiply(crmProductContractRate2.getContractRate().divide(new BigDecimal(100)).subtract(commissionRate)).setScale(2,BigDecimal.ROUND_HALF_UP);    
									}else {
										additionalCommissionAmount = crmPolicyAdditional.getAdditionalAmount().multiply(crmProductContractRate2.getContractRate()).divide(new BigDecimal(100)).multiply(commissionRate).setScale(2,BigDecimal.ROUND_HALF_UP);   
									}
									//保存佣金到佣金计算表
									CrmCommissionCalculation crmCommissionCalculationAdditional = new CrmCommissionCalculation();
									crmCommissionCalculationAdditional.setPolicyId(currentPolicy.getId());		
									crmCommissionCalculationAdditional.setPolicyNo(crmPoliciesDTO.getPolicyNo());	
									crmCommissionCalculationAdditional.setPlanName(crmPolicyAdditional.getPlanName());
									crmCommissionCalculationAdditional.setRateId(crmProductContractRate2.getId());	
									crmCommissionCalculationAdditional.setYearNum(crmProductContractRate2.getYearSeq());				
									crmCommissionCalculationAdditional.setPremiumAmount(crmPolicyAdditional.getAdditionalAmount());				
									crmCommissionCalculationAdditional.setCommissionRate(commissionRate);		
									crmCommissionCalculationAdditional.setCommissionAmount(additionalCommissionAmount);
									crmCommissionCalculationAdditional.setContractRate(crmProductContractRate2.getContractRate());
									crmCommissionCalculationAdditional.setCompanyPayoutRatio(crmProductContractRate2.getContractRate());		
									//crmCommissionCalculationAdditional.setCompanyFee(crmProductContractRate.getCompanyFee());		
									crmCommissionCalculationAdditional.setPaymentCurrency(currentPolicy.getPaymentCurrency());
									crmCommissionCalculationAdditional.setCalculationDate(date);		
									crmCommissionCalculationAdditional.setPayoutStatus("0");
									crmCommissionCalculationAdditional.setSysOrgCode(loginUser.getOrgCode());
									crmCommissionCalculationAdditional.setSaleName(loginUser.getUsername());
									crmCommissionCalculationAdditional.setDelFlag(0);
									crmCommissionCalculationAdditional.setPayoutDate(MyDateUtils.getExpectYear(date, crmProductContractRate2.getYearSeq()-1));


									crmCommissionCalculations.add(crmCommissionCalculationAdditional);	
									
								}
								
							}	

						}

						crmCommissionCalculationService.saveBatch(crmCommissionCalculations);
					}else{
						throw new JeecgBootException("请先设置保单产品对应合约手续费率！");
					}

					//todo 更新客户vip等级信息
					
			
				} else {
					throw new JeecgBootException("当前保单状态不允许操作");
				}
				// 更新主表数据
				this.updateById(crmPolicies);

				// 2. 修改子表CrmPolicyHolders（投保人信息）
				if (crmPolicyHolders != null) {
					// 修改投保人信息
					policyHoldersMapper.updateById(crmPolicyHolders);
				}

				// 3. 修改子表CrmPolicyInsureds（被保人信息）
				if (crmPolicyInsureds != null) {
					// 修改被保人信息
					policyInsuredsMapper.updateById(crmPolicyInsureds);
				}

				// 4. 修改子表CrmPolicyBeneficiaries（受益人信息）
				if (crmPolicyBeneficiaries != null) {
					// 修改受益人信息
					policyBeneficiariesMapper.updateById(crmPolicyBeneficiaries);
				}


				//先删除 后新增
				//删除原附加信息
				
				crmPolicyAdditionalMapper.delete(new LambdaQueryWrapper<CrmPolicyAdditional>().eq(CrmPolicyAdditional::getPolicyId, crmPoliciesDTO.getId()));
				for(CrmPolicyAdditional crmPolicyAdditional:crmPolicyAdditionals) {
					crmPolicyAdditional.setVersion(currentPolicy.getCurrentVersion()); 
					crmPolicyAdditional.setDelFlag(0);
					crmPolicyAdditional.setPolicyId(crmPoliciesDTO.getId());
					crmPolicyAdditional.setId(null);
					crmPolicyAdditionalMapper.insert(crmPolicyAdditional);
				}


			} else {// 订单完成后不能直接修改

				// 更新主表记录为历史版本
				CrmPolicies policy = new CrmPolicies();
				policy.setId(crmPoliciesDTO.getId());
				policy.setIzActive("0");
				policy.setUpdateTime(new Date());
				policy.setUpdateBy(loginUser.getUsername());
				this.updateById(policy);

				crmPolicies.setCurrentVersion(currentPolicy.getCurrentVersion() + 1); // 版本号加1
				crmPolicies.setId(null);
				crmPolicies.setUpdateTime(new Date());
				crmPolicies.setUpdateBy(loginUser.getUsername());
				crmPolicies.setPolicyUuid(currentPolicy.getPolicyUuid());
				crmPolicies.setIzActive("1");
				crmPolicies.setDelFlag(0);

				
				// 保存主表数据
				this.save(crmPolicies);

				// 更新历史版本为失效状态
				policyVersionsMapper.update(null, new UpdateWrapper<CrmPolicyVersions>()
						.eq("policy_uuid", crmPolicies.getPolicyUuid())
						.set("iz_active", "0"));

				// 5. 创建保单版本记录
				CrmPolicyVersions crmPolicyVersions = new CrmPolicyVersions();
				crmPolicyVersions.setPolicyId(crmPolicies.getId());
				crmPolicyVersions.setVersion(crmPolicies.getCurrentVersion()); // 当前版本号
				crmPolicyVersions.setHolderId(crmPolicyHolders != null ? crmPolicyHolders.getId() : null);
				crmPolicyVersions.setInsuredId(crmPolicyInsureds != null ? crmPolicyInsureds.getId() : null);
				crmPolicyVersions
						.setBeneficiaryId(crmPolicyBeneficiaries != null ? crmPolicyBeneficiaries.getId() : null);
				crmPolicyVersions.setEffectiveDate(new Date()); // 设置当前日期为生效日期
				crmPolicyVersions.setDelFlag(0);
				crmPolicyVersions.setPolicyUuid(crmPolicies.getPolicyUuid());
				crmPolicyVersions.setIzActive("1");
				crmPolicyVersions.setChangeReason(crmPoliciesDTO.getChangeReason());
				// 保存版本记录
				policyVersionsMapper.insert(crmPolicyVersions);

				// 4. 更新投保人信息
				if (crmPolicyHolders != null) {
					crmPolicyHolders.setPolicyId(crmPolicies.getId());
					crmPolicyHolders.setVersion(crmPolicies.getCurrentVersion());
					crmPolicyHolders.setDelFlag(0);
					policyHoldersMapper.insert(crmPolicyHolders);
				}

				// 5. 更新被保人信息
				if (crmPolicyInsureds != null) {
					crmPolicyInsureds.setPolicyId(crmPolicies.getId());
					crmPolicyInsureds.setVersion(crmPolicies.getCurrentVersion());
					crmPolicyInsureds.setDelFlag(0);
					policyInsuredsMapper.insert(crmPolicyInsureds);
				}

				// 6. 更新受益人信息
				if (crmPolicyBeneficiaries != null) {
					crmPolicyBeneficiaries.setPolicyId(crmPolicies.getId());
					crmPolicyBeneficiaries.setVersion(crmPolicies.getCurrentVersion());
					crmPolicyBeneficiaries.setDelFlag(0);
					policyBeneficiariesMapper.insert(crmPolicyBeneficiaries);
				}
				//更新附加险信息
				if(crmPolicyAdditionals != null&&crmPolicyAdditionals.size()>0) {
					for(CrmPolicyAdditional crmPolicyAdditional:crmPolicyAdditionals) {
						crmPolicyAdditional.setVersion(crmPolicies.getCurrentVersion()); 
						crmPolicyAdditional.setDelFlag(0);
						crmPolicyAdditional.setPolicyId(crmPolicies.getId());
						crmPolicyAdditional.setId(null);
						crmPolicyAdditionalMapper.insert(crmPolicyAdditional);
					}
				}

			}

		} else {
			throw new JeecgBootException("当前用户不允许操作");
		}

	}

	public boolean sendAnnouncement(String username, SysAnnouncement sysAnnouncement) {
		return false;
	}

	@SensitiveEncode(entity = CrmPoliciesVO.class)
	@Override
	public IPage<CrmPoliciesVO> queryPoliciesInfoEncode(Page<CrmPoliciesVO> page, CrmPoliciesPO crmPoliciesPO) {
		// 调用Mapper层执行分页查询
		return baseMapper.queryPoliciesInfo(page, crmPoliciesPO);
	}

	@Override
	public IPage<CrmPoliciesVO> queryPoliciesInfo(Page<CrmPoliciesVO> page, CrmPoliciesPO crmPoliciesPO) {
		// 调用Mapper层执行分页查询
		return baseMapper.queryPoliciesInfo(page, crmPoliciesPO);
	}

}
