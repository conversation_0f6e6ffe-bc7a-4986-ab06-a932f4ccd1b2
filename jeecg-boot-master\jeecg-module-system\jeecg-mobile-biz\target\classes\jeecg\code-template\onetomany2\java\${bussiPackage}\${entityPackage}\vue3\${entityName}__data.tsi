import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
//列表数据
export const columns: BasicColumn[] = [
 <#list columns as po>
   <#if po.fieldName !='id'>
   {
    title: '${po.filedComment}',
    align:"center",
    <#if po.classType=='date'>
    dataIndex: '${po.fieldName}',
    customRender:({text}) =>{
      return !text?"":(text.length>10?text.substr(0,10):text)
    },
   <#else>
    dataIndex: '${po.fieldName}'
   </#if>
   },
   </#if>
 </#list>
];
//查询数据
export const searchFormSchema: FormSchema[] = [
<#list columns as po>
<#if po.fieldName !='id' && po_index<= tableVo.searchFieldNum>
 {
    label: '${po.filedComment}',
    field: '${po.fieldName}',
     <#if po.fieldType =='date'>
    component: 'DatePicker'
     <#elseif po.fieldType =='datetime'>
    component: 'TimePicker'
     <#elseif "int,decimal,double,"?contains(po.fieldType)>
    component: 'InputNumber'
     <#else>
    component: 'Input'
    </#if>
  },
</#if>
</#list>
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
<#list columns as po><#rt/>
  {
    label: '${po.filedComment}',
    field: '${po.fieldName}',
     <#if po.fieldType =='date'>
    component: 'DatePicker'
     <#elseif po.fieldType =='datetime'>
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
    },
     <#elseif "int,decimal,double,"?contains(po.fieldType)>
    component: 'InputNumber',
     <#else>
    component: 'Input',
    </#if>
    <#if po.fieldName =='id'><#rt/>
    show:false,
    </#if>
  },
</#list>
];

//子表表格配置
<#list subTables as sub>
//列表数据
export const ${sub.entityName?uncap_first}Columns: BasicColumn[] = [
 <#list sub.colums as po><#rt/>
   <#if po.fieldName !='id' && sub.foreignKeys[0]?uncap_first != po.fieldName>
   {
    title: '${po.filedComment}',
    align:"center",
    <#if po.classType=='date'>
    dataIndex: '${po.fieldName}',
    customRender:({text}) =>{
      return !text?"":(text.length>10?text.substr(0,10):text)
    },
   <#else>
    dataIndex: '${po.fieldName}',
   </#if>
   },
   </#if>
 </#list>
];

export const ${sub.entityName?uncap_first}FormSchema: FormSchema[] = [
   // TODO 主键隐藏字段，目前写死为ID
   {label: '', field: 'id', component: 'Input', show: false},
<#-- 循环子表的列 开始 -->
<#list sub.colums as po><#rt/>
<#if po.filedComment !='外键' >
   {
       label: '${po.filedComment}',
       field: '${po.fieldName}',
        <#if po.fieldType =='date'>
       component: 'DatePicker',
        <#elseif po.fieldType =='datetime'>
       component: 'DatePicker',
       componentProps: {
         showTime: true,
         valueFormat: 'YYYY-MM-DD hh:mm:ss',
       },
        <#elseif "int,decimal,double,"?contains(po.fieldType)>
       component: 'InputNumber',
        <#else>
       component: 'Input',
       </#if>
       <#if po.fieldName =='id'><#rt/>
       show:false,
       </#if>
     },
</#if>
</#list>
<#-- 循环子表的列 结束 -->
  ]
</#list>